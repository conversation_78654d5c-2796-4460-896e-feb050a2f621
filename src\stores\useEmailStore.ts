import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/core';
import { Email, ApiResponse, EmailSearchParams, SendEmailParams } from '../types';

interface EmailState {
  emails: Email[];
  currentFolder: string;
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  
  // Actions
  fetchEmails: (params?: EmailSearchParams) => Promise<void>;
  sendEmail: (params: SendEmailParams) => Promise<void>;
  markAsRead: (emailId: string) => Promise<void>;
  markAsUnread: (emailId: string) => Promise<void>;
  toggleFlag: (emailId: string) => Promise<void>;
  deleteEmail: (emailId: string) => Promise<void>;
  setCurrentFolder: (folder: string) => void;
  setPage: (page: number) => void;
  clearError: () => void;
}

export const useEmailStore = create<EmailState>((set, get) => ({
  emails: [],
  currentFolder: 'INBOX',
  loading: false,
  error: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 20,

  fetchEmails: async (params) => {
    set({ loading: true, error: null });
    try {
      const state = get();
      const searchParams = {
        folder: state.currentFolder,
        limit: state.pageSize,
        offset: (state.currentPage - 1) * state.pageSize,
        ...params,
      };

      const response: ApiResponse<Email[]> = await invoke('get_emails', searchParams);
      if (response.success && response.data) {
        set({ 
          emails: response.data, 
          loading: false,
          totalCount: response.data.length // TODO: 从后端获取真实的总数
        });
      } else {
        set({ error: response.error || '获取邮件失败', loading: false });
      }
    } catch (error) {
      set({ error: `获取邮件失败: ${error}`, loading: false });
    }
  },

  sendEmail: async (params) => {
    set({ loading: true, error: null });
    try {
      const response: ApiResponse<string> = await invoke('send_email', params);
      if (response.success) {
        set({ loading: false });
        // 可以选择刷新已发送文件夹
      } else {
        set({ error: response.error || '发送邮件失败', loading: false });
      }
    } catch (error) {
      set({ error: `发送邮件失败: ${error}`, loading: false });
    }
  },

  markAsRead: async (emailId) => {
    try {
      // TODO: 调用后端 API 标记为已读
      set(state => ({
        emails: state.emails.map(email =>
          email.id === emailId ? { ...email, read: true } : email
        )
      }));
    } catch (error) {
      set({ error: `标记已读失败: ${error}` });
    }
  },

  markAsUnread: async (emailId) => {
    try {
      // TODO: 调用后端 API 标记为未读
      set(state => ({
        emails: state.emails.map(email =>
          email.id === emailId ? { ...email, read: false } : email
        )
      }));
    } catch (error) {
      set({ error: `标记未读失败: ${error}` });
    }
  },

  toggleFlag: async (emailId) => {
    try {
      // TODO: 调用后端 API 切换标记状态
      set(state => ({
        emails: state.emails.map(email =>
          email.id === emailId ? { ...email, flagged: !email.flagged } : email
        )
      }));
    } catch (error) {
      set({ error: `切换标记失败: ${error}` });
    }
  },

  deleteEmail: async (emailId) => {
    try {
      // TODO: 调用后端 API 删除邮件
      set(state => ({
        emails: state.emails.filter(email => email.id !== emailId)
      }));
    } catch (error) {
      set({ error: `删除邮件失败: ${error}` });
    }
  },

  setCurrentFolder: (folder) => {
    set({ currentFolder: folder, currentPage: 1 });
    // 自动刷新邮件列表
    get().fetchEmails();
  },

  setPage: (page) => {
    set({ currentPage: page });
    // 自动刷新邮件列表
    get().fetchEmails();
  },

  clearError: () => set({ error: null }),
}));
