import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/core';
import { AppSettings, ApiResponse } from '../types';

interface SettingsState {
  settings: AppSettings;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchSettings: () => Promise<void>;
  updateSettings: (settings: Partial<AppSettings>) => Promise<void>;
  resetSettings: () => Promise<void>;
  clearError: () => void;
}

const defaultSettings: AppSettings = {
  autoRefresh: true,
  refreshInterval: 5,
  notifications: true,
  theme: "auto",
  language: "zh-CN",
  maxEmailsPerPage: 20,
  enableSounds: false,
  dataPath: "",
  logLevel: "info",
};

export const useSettingsStore = create<SettingsState>((set, get) => ({
  settings: defaultSettings,
  loading: false,
  error: null,

  fetchSettings: async () => {
    set({ loading: true, error: null });
    try {
      const response: ApiResponse<AppSettings> = await invoke('get_settings');
      if (response.success && response.data) {
        set({ settings: response.data, loading: false });
      } else {
        set({ error: response.error || '获取设置失败', loading: false });
      }
    } catch (error) {
      set({ error: `获取设置失败: ${error}`, loading: false });
    }
  },

  updateSettings: async (newSettings) => {
    set({ loading: true, error: null });
    try {
      const currentSettings = get().settings;
      const updatedSettings = { ...currentSettings, ...newSettings };
      
      const response: ApiResponse<string> = await invoke('update_settings', { 
        settings: updatedSettings 
      });
      
      if (response.success) {
        set({ settings: updatedSettings, loading: false });
      } else {
        set({ error: response.error || '更新设置失败', loading: false });
      }
    } catch (error) {
      set({ error: `更新设置失败: ${error}`, loading: false });
    }
  },

  resetSettings: async () => {
    set({ loading: true, error: null });
    try {
      const response: ApiResponse<string> = await invoke('update_settings', { 
        settings: defaultSettings 
      });
      
      if (response.success) {
        set({ settings: defaultSettings, loading: false });
      } else {
        set({ error: response.error || '重置设置失败', loading: false });
      }
    } catch (error) {
      set({ error: `重置设置失败: ${error}`, loading: false });
    }
  },

  clearError: () => set({ error: null }),
}));
