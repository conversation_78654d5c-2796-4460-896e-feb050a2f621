import { useEffect, useRef } from 'react';
import { useSettingsStore } from '../stores/useSettingsStore';

interface UseAutoRefreshOptions {
  onRefresh: () => void | Promise<void>;
  enabled?: boolean;
}

export function useAutoRefresh({ onRefresh, enabled = true }: UseAutoRefreshOptions) {
  const { settings } = useSettingsStore();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 清除之前的定时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // 如果启用自动刷新且功能开启
    if (enabled && settings.autoRefresh && settings.refreshInterval > 0) {
      const intervalMs = settings.refreshInterval * 60 * 1000; // 转换为毫秒
      
      intervalRef.current = setInterval(() => {
        onRefresh();
      }, intervalMs);
    }

    // 清理函数
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [settings.autoRefresh, settings.refreshInterval, enabled, onRefresh]);

  // 手动停止自动刷新
  const stopAutoRefresh = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // 手动开始自动刷新
  const startAutoRefresh = () => {
    if (settings.autoRefresh && settings.refreshInterval > 0) {
      const intervalMs = settings.refreshInterval * 60 * 1000;
      intervalRef.current = setInterval(() => {
        onRefresh();
      }, intervalMs);
    }
  };

  return {
    stopAutoRefresh,
    startAutoRefresh,
    isActive: intervalRef.current !== null,
  };
}
