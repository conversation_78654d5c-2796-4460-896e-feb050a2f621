// 邮箱账户类型
export interface EmailAccount {
  id: string;
  name: string;
  email: string;
  provider: EmailProvider;
  imapServer: string;
  imapPort: number;
  smtpServer: string;
  smtpPort: number;
  useSSL: boolean;
  appPassword: string; // 加密存储
  status: AccountStatus;
  createdAt: string;
  updatedAt: string;
}

// 邮箱服务商类型
export type EmailProvider = "Gmail" | "Outlook" | "QQ" | "163" | "126" | "Other";

// 账户状态
export type AccountStatus = "connected" | "disconnected" | "error" | "testing";

// 邮件类型
export interface Email {
  id: string;
  messageId: string;
  accountId: string;
  from: EmailAddress;
  to: EmailAddress[];
  cc?: EmailAddress[];
  bcc?: EmailAddress[];
  subject: string;
  body: string;
  htmlBody?: string;
  date: string;
  read: boolean;
  flagged: boolean;
  hasAttachment: boolean;
  attachments?: Attachment[];
  folder: string;
  size: number;
}

// 邮件地址类型
export interface EmailAddress {
  name?: string;
  email: string;
}

// 附件类型
export interface Attachment {
  id: string;
  filename: string;
  contentType: string;
  size: number;
  cid?: string; // Content-ID for inline attachments
}

// 邮件文件夹类型
export interface EmailFolder {
  name: string;
  displayName: string;
  messageCount: number;
  unreadCount: number;
  type: FolderType;
}

export type FolderType = "inbox" | "sent" | "drafts" | "trash" | "spam" | "custom";

// 应用设置类型
export interface AppSettings {
  autoRefresh: boolean;
  refreshInterval: number; // 分钟
  notifications: boolean;
  theme: "light" | "dark" | "auto";
  language: "zh-CN" | "en-US";
  maxEmailsPerPage: number;
  enableSounds: boolean;
  dataPath: string;
  logLevel: "error" | "warn" | "info" | "debug";
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 邮件搜索参数
export interface EmailSearchParams {
  accountId?: string;
  folder?: string;
  query?: string;
  from?: string;
  to?: string;
  subject?: string;
  dateFrom?: string;
  dateTo?: string;
  read?: boolean;
  hasAttachment?: boolean;
  limit?: number;
  offset?: number;
}

// 邮件发送参数
export interface SendEmailParams {
  accountId: string;
  to: EmailAddress[];
  cc?: EmailAddress[];
  bcc?: EmailAddress[];
  subject: string;
  body: string;
  htmlBody?: string;
  attachments?: File[];
}

// Tauri 命令响应类型
export interface TauriCommand<T = any> {
  invoke: (command: string, args?: Record<string, any>) => Promise<ApiResponse<T>>;
}
