use serde::{Deserialize, Serialize};
use std::path::PathBuf;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Ser<PERSON><PERSON>, Deserialize)]
pub struct AppSettings {
    pub auto_refresh: bool,
    pub refresh_interval: u32, // 分钟
    pub notifications: bool,
    pub theme: Theme,
    pub language: Language,
    pub max_emails_per_page: u32,
    pub enable_sounds: bool,
    pub data_path: PathBuf,
    pub log_level: LogLevel,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum Theme {
    Light,
    Dark,
    Auto,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum Language {
    #[serde(rename = "zh-CN")]
    Chinese,
    #[serde(rename = "en-US")]
    English,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum LogLevel {
    Error,
    Warn,
    Info,
    Debug,
}

impl Default for AppSettings {
    fn default() -> Self {
        Self {
            auto_refresh: true,
            refresh_interval: 5,
            notifications: true,
            theme: Theme::Auto,
            language: Language::Chinese,
            max_emails_per_page: 20,
            enable_sounds: false,
            data_path: PathBuf::from("./data"),
            log_level: LogLevel::Info,
        }
    }
}

impl AppSettings {
    pub fn validate(&self) -> Result<(), String> {
        if self.refresh_interval == 0 || self.refresh_interval > 60 {
            return Err("刷新间隔必须在1-60分钟之间".to_string());
        }

        if self.max_emails_per_page == 0 || self.max_emails_per_page > 100 {
            return Err("每页邮件数量必须在1-100之间".to_string());
        }

        Ok(())
    }

    pub fn get_refresh_interval_ms(&self) -> u64 {
        (self.refresh_interval as u64) * 60 * 1000
    }
}

// 邮箱服务商预设配置
#[derive(Debug, Clone)]
pub struct EmailProviderConfig {
    pub name: String,
    pub imap_server: String,
    pub imap_port: u16,
    pub smtp_server: String,
    pub smtp_port: u16,
    pub use_ssl: bool,
    pub auth_method: AuthMethod,
}

#[derive(Debug, Clone)]
pub enum AuthMethod {
    OAuth2,
    AppPassword,
    PlainPassword,
}

impl EmailProviderConfig {
    pub fn get_gmail_config() -> Self {
        Self {
            name: "Gmail".to_string(),
            imap_server: "imap.gmail.com".to_string(),
            imap_port: 993,
            smtp_server: "smtp.gmail.com".to_string(),
            smtp_port: 587,
            use_ssl: true,
            auth_method: AuthMethod::AppPassword,
        }
    }

    pub fn get_outlook_config() -> Self {
        Self {
            name: "Outlook".to_string(),
            imap_server: "outlook.office365.com".to_string(),
            imap_port: 993,
            smtp_server: "smtp-mail.outlook.com".to_string(),
            smtp_port: 587,
            use_ssl: true,
            auth_method: AuthMethod::AppPassword,
        }
    }

    pub fn get_qq_config() -> Self {
        Self {
            name: "QQ邮箱".to_string(),
            imap_server: "imap.qq.com".to_string(),
            imap_port: 993,
            smtp_server: "smtp.qq.com".to_string(),
            smtp_port: 587,
            use_ssl: true,
            auth_method: AuthMethod::AppPassword,
        }
    }

    pub fn get_163_config() -> Self {
        Self {
            name: "163邮箱".to_string(),
            imap_server: "imap.163.com".to_string(),
            imap_port: 993,
            smtp_server: "smtp.163.com".to_string(),
            smtp_port: 587,
            use_ssl: true,
            auth_method: AuthMethod::AppPassword,
        }
    }

    pub fn get_126_config() -> Self {
        Self {
            name: "126邮箱".to_string(),
            imap_server: "imap.126.com".to_string(),
            imap_port: 993,
            smtp_server: "smtp.126.com".to_string(),
            smtp_port: 587,
            use_ssl: true,
            auth_method: AuthMethod::AppPassword,
        }
    }

    pub fn get_all_configs() -> Vec<Self> {
        vec![
            Self::get_gmail_config(),
            Self::get_outlook_config(),
            Self::get_qq_config(),
            Self::get_163_config(),
            Self::get_126_config(),
        ]
    }
}
