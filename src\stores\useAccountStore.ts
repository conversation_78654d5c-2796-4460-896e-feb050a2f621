import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/core';
import { EmailAccount, ApiResponse } from '../types';

interface AccountState {
  accounts: EmailAccount[];
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchAccounts: () => Promise<void>;
  addAccount: (account: Omit<EmailAccount, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateAccount: (account: EmailAccount) => Promise<void>;
  deleteAccount: (accountId: string) => Promise<void>;
  testConnection: (account: EmailAccount) => Promise<boolean>;
  clearError: () => void;
}

export const useAccountStore = create<AccountState>((set, get) => ({
  accounts: [],
  loading: false,
  error: null,

  fetchAccounts: async () => {
    set({ loading: true, error: null });
    try {
      const response: ApiResponse<EmailAccount[]> = await invoke('get_accounts');
      if (response.success && response.data) {
        set({ accounts: response.data, loading: false });
      } else {
        set({ error: response.error || '获取账户列表失败', loading: false });
      }
    } catch (error) {
      set({ error: `获取账户列表失败: ${error}`, loading: false });
    }
  },

  addAccount: async (accountData) => {
    set({ loading: true, error: null });
    try {
      const response: ApiResponse<string> = await invoke('add_account', { account: accountData });
      if (response.success) {
        // 重新获取账户列表
        await get().fetchAccounts();
      } else {
        set({ error: response.error || '添加账户失败', loading: false });
      }
    } catch (error) {
      set({ error: `添加账户失败: ${error}`, loading: false });
    }
  },

  updateAccount: async (account) => {
    set({ loading: true, error: null });
    try {
      const response: ApiResponse<string> = await invoke('update_account', { account });
      if (response.success) {
        // 更新本地状态
        set(state => ({
          accounts: state.accounts.map(acc => 
            acc.id === account.id ? account : acc
          ),
          loading: false
        }));
      } else {
        set({ error: response.error || '更新账户失败', loading: false });
      }
    } catch (error) {
      set({ error: `更新账户失败: ${error}`, loading: false });
    }
  },

  deleteAccount: async (accountId) => {
    set({ loading: true, error: null });
    try {
      const response: ApiResponse<string> = await invoke('delete_account', { accountId });
      if (response.success) {
        // 从本地状态中移除
        set(state => ({
          accounts: state.accounts.filter(acc => acc.id !== accountId),
          loading: false
        }));
      } else {
        set({ error: response.error || '删除账户失败', loading: false });
      }
    } catch (error) {
      set({ error: `删除账户失败: ${error}`, loading: false });
    }
  },

  testConnection: async (account) => {
    set({ loading: true, error: null });
    try {
      const response: ApiResponse<string> = await invoke('test_connection', { account });
      set({ loading: false });
      return response.success;
    } catch (error) {
      set({ error: `连接测试失败: ${error}`, loading: false });
      return false;
    }
  },

  clearError: () => set({ error: null }),
}));
