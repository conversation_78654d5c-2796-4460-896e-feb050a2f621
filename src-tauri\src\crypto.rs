use anyhow::{Result, anyhow};
use ring::{aead, pbkdf2, rand::{SecureRandom, SystemRandom}};
use base64::{Engine as _, engine::general_purpose};

const CREDENTIAL_LEN: usize = aead::AES_256_GCM.key_len();
const NONCE_LEN: usize = aead::AES_256_GCM.nonce_len();
const SALT_LEN: usize = 32;
const PBKDF2_ITERATIONS: u32 = 100_000;

pub struct Crypto {
    rng: SystemRandom,
}

impl Crypto {
    pub fn new() -> Self {
        Self {
            rng: SystemRandom::new(),
        }
    }

    /// 加密敏感数据（如应用专用密码）
    pub fn encrypt(&self, data: &str, master_key: &str) -> Result<String> {
        // 生成随机盐
        let mut salt = [0u8; SALT_LEN];
        self.rng.fill(&mut salt)?;

        // 从主密钥派生加密密钥
        let mut key = [0u8; CREDENTIAL_LEN];
        pbkdf2::derive(
            pbkdf2::PBKDF2_HMAC_SHA256,
            std::num::NonZeroU32::new(PBKDF2_ITERATIONS).unwrap(),
            &salt,
            master_key.as_bytes(),
            &mut key,
        );

        // 生成随机 nonce
        let mut nonce = [0u8; NONCE_LEN];
        self.rng.fill(&mut nonce)?;

        // 创建加密密钥
        let unbound_key = aead::UnboundKey::new(&aead::AES_256_GCM, &key)?;
        let sealing_key = aead::LessSafeKey::new(unbound_key);

        // 加密数据
        let mut in_out = data.as_bytes().to_vec();
        let tag = sealing_key.seal_in_place_separate_tag(
            aead::Nonce::assume_unique_for_key(nonce),
            aead::Aad::empty(),
            &mut in_out,
        )?;

        // 组合结果：salt + nonce + ciphertext + tag
        let mut result = Vec::new();
        result.extend_from_slice(&salt);
        result.extend_from_slice(&nonce);
        result.extend_from_slice(&in_out);
        result.extend_from_slice(tag.as_ref());

        // Base64 编码
        Ok(general_purpose::STANDARD.encode(&result))
    }

    /// 解密敏感数据
    pub fn decrypt(&self, encrypted_data: &str, master_key: &str) -> Result<String> {
        // Base64 解码
        let data = general_purpose::STANDARD.decode(encrypted_data)?;

        if data.len() < SALT_LEN + NONCE_LEN + aead::AES_256_GCM.tag_len() {
            return Err(anyhow!("Invalid encrypted data length"));
        }

        // 提取组件
        let salt = &data[0..SALT_LEN];
        let nonce = &data[SALT_LEN..SALT_LEN + NONCE_LEN];
        let ciphertext_and_tag = &data[SALT_LEN + NONCE_LEN..];

        // 从主密钥派生解密密钥
        let mut key = [0u8; CREDENTIAL_LEN];
        pbkdf2::derive(
            pbkdf2::PBKDF2_HMAC_SHA256,
            std::num::NonZeroU32::new(PBKDF2_ITERATIONS).unwrap(),
            salt,
            master_key.as_bytes(),
            &mut key,
        );

        // 创建解密密钥
        let unbound_key = aead::UnboundKey::new(&aead::AES_256_GCM, &key)?;
        let opening_key = aead::LessSafeKey::new(unbound_key);

        // 解密数据
        let mut in_out = ciphertext_and_tag.to_vec();
        let plaintext = opening_key.open_in_place(
            aead::Nonce::try_assume_unique_for_key(nonce)?,
            aead::Aad::empty(),
            &mut in_out,
        )?;

        // 转换为字符串
        String::from_utf8(plaintext.to_vec())
            .map_err(|e| anyhow!("Failed to convert decrypted data to string: {}", e))
    }

    /// 生成安全的主密钥
    pub fn generate_master_key(&self) -> Result<String> {
        let mut key = [0u8; 32];
        self.rng.fill(&mut key)?;
        Ok(general_purpose::STANDARD.encode(&key))
    }

    /// 验证主密钥强度
    pub fn validate_master_key(key: &str) -> bool {
        // 基本验证：长度至少8位，包含字母和数字
        key.len() >= 8 
            && key.chars().any(|c| c.is_alphabetic())
            && key.chars().any(|c| c.is_numeric())
    }
}

impl Default for Crypto {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encrypt_decrypt() {
        let crypto = Crypto::new();
        let master_key = "test_master_key_123";
        let original_data = "sensitive_password_123";

        let encrypted = crypto.encrypt(original_data, master_key).unwrap();
        let decrypted = crypto.decrypt(&encrypted, master_key).unwrap();

        assert_eq!(original_data, decrypted);
    }

    #[test]
    fn test_master_key_validation() {
        assert!(Crypto::validate_master_key("password123"));
        assert!(Crypto::validate_master_key("MySecureKey1"));
        assert!(!Crypto::validate_master_key("password")); // 没有数字
        assert!(!Crypto::validate_master_key("123456")); // 没有字母
        assert!(!Crypto::validate_master_key("pass1")); // 太短
    }
}
