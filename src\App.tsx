import { useState, useEffect } from "react";
import { Routes, Route } from "react-router-dom";
import { Layout, Menu, theme } from "antd";
import { 
  MailOutlined, 
  SettingOutlined, 
  UserOutlined,
  InboxOutlined,
  SendOutlined 
} from "@ant-design/icons";
import { invoke } from "@tauri-apps/api/core";
import AccountManagement from "./pages/AccountManagement";
import EmailList from "./pages/EmailList";
import Settings from "./pages/Settings";
import "./styles/App.css";

const { Header, Sider, Content } = Layout;

function App() {
  const [collapsed, setCollapsed] = useState(false);
  const [greetMsg, setGreetMsg] = useState("");
  const [name, setName] = useState("");
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  async function greet() {
    // Learn more about Tauri commands at https://tauri.app/v1/guides/features/command
    setGreetMsg(await invoke("greet", { name }));
  }

  const menuItems = [
    {
      key: "inbox",
      icon: <InboxOutlined />,
      label: "收件箱",
    },
    {
      key: "sent",
      icon: <SendOutlined />,
      label: "已发送",
    },
    {
      key: "accounts",
      icon: <UserOutlined />,
      label: "账户管理",
    },
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: "设置",
    },
  ];

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <Sider trigger={null} collapsible collapsed={collapsed}>
        <div className="demo-logo-vertical" />
        <Menu
          theme="dark"
          mode="inline"
          defaultSelectedKeys={["inbox"]}
          items={menuItems}
        />
      </Sider>
      <Layout>
        <Header style={{ padding: 0, background: colorBgContainer }}>
          <div className="header-content">
            <MailOutlined className="header-icon" />
            <h1>邮箱工具</h1>
          </div>
        </Header>
        <Content
          style={{
            margin: "24px 16px",
            padding: 24,
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
        >
          <Routes>
            <Route path="/" element={<EmailList />} />
            <Route path="/accounts" element={<AccountManagement />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
}

export default App;
