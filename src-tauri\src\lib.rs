use tauri::Manager;

mod commands;
mod database;
mod email;
mod crypto;
mod config;

use commands::*;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_shell::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            get_accounts,
            add_account,
            update_account,
            delete_account,
            test_connection,
            get_emails,
            send_email,
            get_settings,
            update_settings,
            fetch_email_body,
            mark_email_as_read,
            mark_email_as_unread,
            toggle_email_flag,
            get_folders
        ])
        .setup(|app| {
            // 初始化数据库
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                if let Err(e) = database::init_database(&app_handle).await {
                    eprintln!("Failed to initialize database: {}", e);
                }
            });
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
