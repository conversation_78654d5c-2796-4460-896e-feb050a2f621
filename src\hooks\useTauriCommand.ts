import { useState, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { ApiResponse } from '../types';

interface UseTauriCommandOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  showLoading?: boolean;
}

export function useTauriCommand<T = any>(
  command: string,
  options: UseTauriCommandOptions = {}
) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<T | null>(null);

  const execute = useCallback(async (args?: Record<string, any>) => {
    if (options.showLoading !== false) {
      setLoading(true);
    }
    setError(null);

    try {
      const response: ApiResponse<T> = await invoke(command, args);
      
      if (response.success && response.data !== undefined) {
        setData(response.data);
        options.onSuccess?.(response.data);
        return response.data;
      } else {
        const errorMsg = response.error || `命令 ${command} 执行失败`;
        setError(errorMsg);
        options.onError?.(errorMsg);
        throw new Error(errorMsg);
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : String(err);
      setError(errorMsg);
      options.onError?.(errorMsg);
      throw err;
    } finally {
      if (options.showLoading !== false) {
        setLoading(false);
      }
    }
  }, [command, options]);

  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
    setData(null);
  }, []);

  return {
    execute,
    loading,
    error,
    data,
    reset,
  };
}
