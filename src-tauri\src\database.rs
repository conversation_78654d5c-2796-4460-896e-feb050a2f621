use sqlx::{Sqlite<PERSON><PERSON>, Row};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use std::path::PathBuf;
use anyhow::Result;
use crate::email::EmailAccount;
use crate::crypto::Crypto;

pub struct Database {
    pool: SqlitePool,
}

impl Database {
    pub async fn new(database_url: &str) -> Result<Self> {
        let pool = SqlitePool::connect(database_url).await?;
        
        // 运行数据库迁移
        sqlx::migrate!("./migrations").run(&pool).await?;
        
        Ok(Self { pool })
    }

    pub fn pool(&self) -> &SqlitePool {
        &self.pool
    }
}

pub async fn init_database(app_handle: &AppHandle) -> Result<()> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .expect("Failed to get app data directory");
    
    // 确保应用数据目录存在
    std::fs::create_dir_all(&app_dir)?;
    
    let database_path = app_dir.join("email_tool.db");
    let database_url = format!("sqlite:{}", database_path.display());
    
    let database = Database::new(&database_url).await?;
    app_handle.manage(database);
    
    println!("Database initialized at: {}", database_path.display());
    Ok(())
}

// 数据库迁移 SQL
pub const INIT_SQL: &str = r#"
-- 邮箱账户表
CREATE TABLE IF NOT EXISTS email_accounts (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    provider TEXT NOT NULL,
    imap_server TEXT NOT NULL,
    imap_port INTEGER NOT NULL,
    smtp_server TEXT NOT NULL,
    smtp_port INTEGER NOT NULL,
    use_ssl BOOLEAN NOT NULL DEFAULT 1,
    app_password_encrypted TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'disconnected',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 邮件表
CREATE TABLE IF NOT EXISTS emails (
    id TEXT PRIMARY KEY,
    message_id TEXT NOT NULL,
    account_id TEXT NOT NULL,
    from_email TEXT NOT NULL,
    from_name TEXT,
    to_emails TEXT NOT NULL, -- JSON array
    cc_emails TEXT, -- JSON array
    bcc_emails TEXT, -- JSON array
    subject TEXT NOT NULL,
    body TEXT,
    html_body TEXT,
    date_received DATETIME NOT NULL,
    is_read BOOLEAN NOT NULL DEFAULT 0,
    is_flagged BOOLEAN NOT NULL DEFAULT 0,
    has_attachment BOOLEAN NOT NULL DEFAULT 0,
    folder TEXT NOT NULL DEFAULT 'INBOX',
    size_bytes INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES email_accounts (id) ON DELETE CASCADE
);

-- 附件表
CREATE TABLE IF NOT EXISTS attachments (
    id TEXT PRIMARY KEY,
    email_id TEXT NOT NULL,
    filename TEXT NOT NULL,
    content_type TEXT NOT NULL,
    size_bytes INTEGER NOT NULL,
    content_id TEXT, -- For inline attachments
    file_path TEXT, -- Local storage path
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (email_id) REFERENCES emails (id) ON DELETE CASCADE
);

-- 应用设置表
CREATE TABLE IF NOT EXISTS app_settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_emails_account_id ON emails(account_id);
CREATE INDEX IF NOT EXISTS idx_emails_date_received ON emails(date_received);
CREATE INDEX IF NOT EXISTS idx_emails_is_read ON emails(is_read);
CREATE INDEX IF NOT EXISTS idx_emails_folder ON emails(folder);
CREATE INDEX IF NOT EXISTS idx_attachments_email_id ON attachments(email_id);

-- 插入默认设置
INSERT OR IGNORE INTO app_settings (key, value) VALUES 
    ('auto_refresh', 'true'),
    ('refresh_interval', '5'),
    ('notifications', 'true'),
    ('theme', 'auto'),
    ('language', 'zh-CN'),
    ('max_emails_per_page', '20'),
    ('enable_sounds', 'false'),
    ('log_level', 'info');
"#;
