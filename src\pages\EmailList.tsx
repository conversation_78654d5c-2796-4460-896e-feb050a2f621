import { useState, useEffect } from "react";
import { <PERSON>, Card, Button, Space, Tag, Typography } from "antd";
import { ReloadOutlined, MailOutlined } from "@ant-design/icons";

const { Title } = Typography;

interface Email {
  id: string;
  from: string;
  subject: string;
  date: string;
  read: boolean;
  hasAttachment: boolean;
}

const EmailList = () => {
  const [emails, setEmails] = useState<Email[]>([]);
  const [loading, setLoading] = useState(false);

  // 模拟邮件数据
  const mockEmails: Email[] = [
    {
      id: "1",
      from: "<EMAIL>",
      subject: "欢迎使用邮箱工具",
      date: "2024-01-15 10:30",
      read: false,
      hasAttachment: false,
    },
    {
      id: "2", 
      from: "<EMAIL>",
      subject: "系统通知：账户设置已更新",
      date: "2024-01-14 15:45",
      read: true,
      hasAttachment: true,
    },
  ];

  useEffect(() => {
    setEmails(mockEmails);
  }, []);

  const handleRefresh = async () => {
    setLoading(true);
    // TODO: 调用 Tauri 命令获取邮件
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  const columns = [
    {
      title: "发件人",
      dataIndex: "from",
      key: "from",
      width: 200,
    },
    {
      title: "主题",
      dataIndex: "subject",
      key: "subject",
      render: (text: string, record: Email) => (
        <span style={{ fontWeight: record.read ? "normal" : "bold" }}>
          {text}
        </span>
      ),
    },
    {
      title: "日期",
      dataIndex: "date",
      key: "date",
      width: 150,
    },
    {
      title: "状态",
      key: "status",
      width: 100,
      render: (record: Email) => (
        <Space>
          {!record.read && <Tag color="blue">未读</Tag>}
          {record.hasAttachment && <Tag color="orange">附件</Tag>}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Title level={3} style={{ margin: 0 }}>
            <MailOutlined style={{ marginRight: 8 }} />
            收件箱
          </Title>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />} 
            loading={loading}
            onClick={handleRefresh}
          >
            刷新
          </Button>
        </div>
        
        <Table
          columns={columns}
          dataSource={emails}
          rowKey="id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 封邮件`,
          }}
          onRow={(record) => ({
            onClick: () => {
              console.log("点击邮件:", record);
              // TODO: 打开邮件详情
            },
            style: { cursor: "pointer" },
          })}
        />
      </Card>
    </div>
  );
};

export default EmailList;
