use anyhow::{Result, anyhow};
use async_imap::{Session, Client};
use lettre::{SmtpTransport, Transport, Message, message::header::ContentType};
use lettre::transport::smtp::authentication::Credentials;
use native_tls::TlsConnector;
use std::net::TcpStream;
use chrono::{DateTime, Utc};
use serde_json;

use super::{EmailAccount, EmailMessage, EmailAddress, SendEmailRequest, Attachment};

pub struct EmailClient {
    account: EmailAccount,
}

impl EmailClient {
    pub fn new(account: EmailAccount) -> Self {
        Self { account }
    }

    pub async fn test_connection(&self) -> Result<()> {
        // 测试 IMAP 连接
        self.test_imap_connection().await?;
        
        // 测试 SMTP 连接
        self.test_smtp_connection().await?;
        
        Ok(())
    }

    async fn test_imap_connection(&self) -> Result<()> {
        let tls = TlsConnector::builder().build()?;
        let client = async_imap::connect(
            (&self.account.imap_server[..], self.account.imap_port),
            &self.account.imap_server,
            &tls,
        ).await?;

        let mut session = client
            .login(&self.account.email, &self.account.app_password)
            .await
            .map_err(|e| anyhow!("IMAP login failed: {:?}", e))?;

        // 测试选择收件箱
        session.select("INBOX").await?;
        
        // 登出
        session.logout().await?;
        
        Ok(())
    }

    async fn test_smtp_connection(&self) -> Result<()> {
        let creds = Credentials::new(
            self.account.email.clone(),
            self.account.app_password.clone(),
        );

        let mailer = SmtpTransport::relay(&self.account.smtp_server)?
            .credentials(creds)
            .build();

        // 测试连接（不发送邮件）
        mailer.test_connection()?;
        
        Ok(())
    }

    pub async fn fetch_emails(&self, folder: &str, limit: Option<u32>) -> Result<Vec<EmailMessage>> {
        let tls = TlsConnector::builder().build()?;
        let client = async_imap::connect(
            (&self.account.imap_server[..], self.account.imap_port),
            &self.account.imap_server,
            &tls,
        ).await?;

        let mut session = client
            .login(&self.account.email, &self.account.app_password)
            .await
            .map_err(|e| anyhow!("IMAP login failed: {:?}", e))?;

        session.select(folder).await?;

        // 获取邮件数量
        let mailbox = session.examine(folder).await?;
        let total_messages = mailbox.exists;

        if total_messages == 0 {
            session.logout().await?;
            return Ok(vec![]);
        }

        // 计算要获取的邮件范围
        let fetch_limit = limit.unwrap_or(50).min(total_messages);
        let start = if total_messages > fetch_limit {
            total_messages - fetch_limit + 1
        } else {
            1
        };

        // 获取邮件头信息和标志
        let messages = session
            .fetch(
                format!("{}:{}", start, total_messages),
                "ENVELOPE BODY[HEADER] FLAGS RFC822.SIZE"
            )
            .await?;

        let mut emails = Vec::new();

        for message in messages.iter() {
            if let Some(envelope) = message.envelope() {
                // 解析发件人
                let from = if let Some(from_list) = envelope.from.as_ref() {
                    if let Some(first_from) = from_list.first() {
                        let name = first_from.name.as_ref()
                            .map(|n| String::from_utf8_lossy(n).to_string());
                        let email = first_from.mailbox.as_ref()
                            .zip(first_from.host.as_ref())
                            .map(|(mailbox, host)| {
                                format!("{}@{}",
                                    String::from_utf8_lossy(mailbox),
                                    String::from_utf8_lossy(host)
                                )
                            })
                            .unwrap_or_default();

                        EmailAddress {
                            name,
                            email,
                        }
                    } else {
                        EmailAddress::new("<EMAIL>".to_string())
                    }
                } else {
                    EmailAddress::new("<EMAIL>".to_string())
                };

                // 解析收件人
                let to = if let Some(to_list) = envelope.to.as_ref() {
                    to_list.iter().filter_map(|addr| {
                        addr.mailbox.as_ref()
                            .zip(addr.host.as_ref())
                            .map(|(mailbox, host)| {
                                let name = addr.name.as_ref()
                                    .map(|n| String::from_utf8_lossy(n).to_string());
                                let email = format!("{}@{}",
                                    String::from_utf8_lossy(mailbox),
                                    String::from_utf8_lossy(host)
                                );
                                EmailAddress { name, email }
                            })
                    }).collect()
                } else {
                    vec![]
                };

                // 解析主题
                let subject = envelope.subject.as_ref()
                    .map(|s| String::from_utf8_lossy(s).to_string())
                    .unwrap_or_default();

                // 解析日期
                let date = envelope.date.as_ref()
                    .and_then(|d| {
                        let date_str = String::from_utf8_lossy(d);
                        chrono::DateTime::parse_from_rfc2822(&date_str)
                            .map(|dt| dt.with_timezone(&Utc))
                            .ok()
                    })
                    .unwrap_or_else(Utc::now);

                // 获取邮件标志
                let flags = message.flags();
                let read = flags.iter().any(|f| matches!(f, async_imap::types::Flag::Seen));
                let flagged = flags.iter().any(|f| matches!(f, async_imap::types::Flag::Flagged));

                // 获取邮件大小
                let size = message.rfc822_size().unwrap_or(0) as u64;

                let email = EmailMessage {
                    id: uuid::Uuid::new_v4().to_string(),
                    message_id: format!("msg_{}", message.message),
                    account_id: self.account.id.clone(),
                    from,
                    to,
                    cc: None, // TODO: 解析 CC
                    bcc: None, // TODO: 解析 BCC
                    subject,
                    body: "".to_string(), // 需要单独获取邮件正文
                    html_body: None,
                    date,
                    read,
                    flagged,
                    has_attachment: false, // TODO: 检查是否有附件
                    attachments: None,
                    folder: folder.to_string(),
                    size,
                };
                emails.push(email);
            }
        }

        session.logout().await?;
        Ok(emails)
    }

    pub async fn fetch_email_body(&self, folder: &str, message_id: u32) -> Result<(String, Option<String>)> {
        let tls = TlsConnector::builder().build()?;
        let client = async_imap::connect(
            (&self.account.imap_server[..], self.account.imap_port),
            &self.account.imap_server,
            &tls,
        ).await?;

        let mut session = client
            .login(&self.account.email, &self.account.app_password)
            .await
            .map_err(|e| anyhow!("IMAP login failed: {:?}", e))?;

        session.select(folder).await?;

        // 获取邮件正文
        let messages = session
            .fetch(message_id.to_string(), "BODY[TEXT]")
            .await?;

        let mut text_body = String::new();
        let mut html_body: Option<String> = None;

        if let Some(message) = messages.iter().next() {
            if let Some(body) = message.body() {
                // 简单的文本解析，实际应用中需要更复杂的MIME解析
                text_body = String::from_utf8_lossy(body).to_string();

                // 如果包含HTML标签，则认为是HTML邮件
                if text_body.contains("<html>") || text_body.contains("<HTML>") {
                    html_body = Some(text_body.clone());
                    // 从HTML中提取纯文本（简化版本）
                    text_body = text_body
                        .replace("<br>", "\n")
                        .replace("<BR>", "\n")
                        .replace("<p>", "\n")
                        .replace("<P>", "\n");
                    // 移除HTML标签（简化版本）
                    text_body = regex::Regex::new(r"<[^>]*>")
                        .unwrap()
                        .replace_all(&text_body, "")
                        .to_string();
                }
            }
        }

        session.logout().await?;
        Ok((text_body, html_body))
    }

    pub async fn mark_as_read(&self, folder: &str, message_id: u32) -> Result<()> {
        let tls = TlsConnector::builder().build()?;
        let client = async_imap::connect(
            (&self.account.imap_server[..], self.account.imap_port),
            &self.account.imap_server,
            &tls,
        ).await?;

        let mut session = client
            .login(&self.account.email, &self.account.app_password)
            .await
            .map_err(|e| anyhow!("IMAP login failed: {:?}", e))?;

        session.select(folder).await?;
        session.store(message_id.to_string(), "+FLAGS (\\Seen)").await?;
        session.logout().await?;
        Ok(())
    }

    pub async fn mark_as_unread(&self, folder: &str, message_id: u32) -> Result<()> {
        let tls = TlsConnector::builder().build()?;
        let client = async_imap::connect(
            (&self.account.imap_server[..], self.account.imap_port),
            &self.account.imap_server,
            &tls,
        ).await?;

        let mut session = client
            .login(&self.account.email, &self.account.app_password)
            .await
            .map_err(|e| anyhow!("IMAP login failed: {:?}", e))?;

        session.select(folder).await?;
        session.store(message_id.to_string(), "-FLAGS (\\Seen)").await?;
        session.logout().await?;
        Ok(())
    }

    pub async fn toggle_flag(&self, folder: &str, message_id: u32, flagged: bool) -> Result<()> {
        let tls = TlsConnector::builder().build()?;
        let client = async_imap::connect(
            (&self.account.imap_server[..], self.account.imap_port),
            &self.account.imap_server,
            &tls,
        ).await?;

        let mut session = client
            .login(&self.account.email, &self.account.app_password)
            .await
            .map_err(|e| anyhow!("IMAP login failed: {:?}", e))?;

        session.select(folder).await?;

        let flag_action = if flagged {
            "+FLAGS (\\Flagged)"
        } else {
            "-FLAGS (\\Flagged)"
        };

        session.store(message_id.to_string(), flag_action).await?;
        session.logout().await?;
        Ok(())
    }

    pub async fn send_email(&self, request: SendEmailRequest) -> Result<()> {
        // 构建邮件
        let mut email_builder = Message::builder()
            .from(self.account.email.parse()?)
            .subject(&request.subject);

        // 添加收件人
        for to_addr in &request.to {
            email_builder = email_builder.to(to_addr.email.parse()?);
        }

        // 添加抄送
        if let Some(cc_addrs) = &request.cc {
            for cc_addr in cc_addrs {
                email_builder = email_builder.cc(cc_addr.email.parse()?);
            }
        }

        // 添加密送
        if let Some(bcc_addrs) = &request.bcc {
            for bcc_addr in bcc_addrs {
                email_builder = email_builder.bcc(bcc_addr.email.parse()?);
            }
        }

        // 设置邮件内容
        let email = if let Some(html_body) = &request.html_body {
            email_builder
                .header(ContentType::TEXT_HTML)
                .body(html_body.clone())?
        } else {
            email_builder
                .header(ContentType::TEXT_PLAIN)
                .body(request.body.clone())?
        };

        // 创建 SMTP 传输
        let creds = Credentials::new(
            self.account.email.clone(),
            self.account.app_password.clone(),
        );

        let mailer = SmtpTransport::relay(&self.account.smtp_server)?
            .credentials(creds)
            .build();

        // 发送邮件
        mailer.send(&email)?;

        Ok(())
    }

    pub async fn get_folders(&self) -> Result<Vec<String>> {
        let tls = TlsConnector::builder().build()?;
        let client = async_imap::connect(
            (&self.account.imap_server[..], self.account.imap_port),
            &self.account.imap_server,
            &tls,
        ).await?;

        let mut session = client
            .login(&self.account.email, &self.account.app_password)
            .await
            .map_err(|e| anyhow!("IMAP login failed: {:?}", e))?;

        let folders = session.list(None, Some("*")).await?;
        let folder_names: Vec<String> = folders
            .iter()
            .map(|folder| folder.name().to_string())
            .collect();

        session.logout().await?;
        Ok(folder_names)
    }
}
