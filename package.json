{"name": "email-tool", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@tauri-apps/api": "^2.0.0", "@tauri-apps/plugin-shell": "^2.0.0", "antd": "^5.12.0", "zustand": "^4.4.0", "react-router-dom": "^6.20.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "typescript": "^5.0.0", "vite": "^5.0.0", "@tauri-apps/cli": "^2.0.0"}}