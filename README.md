# 邮箱工具

一个基于 Tauri + React + TypeScript 构建的现代化邮箱客户端。

## 功能特性

- 🔐 **安全认证**: 支持应用专用密码，安全存储凭据
- 📧 **多账户管理**: 支持 Gmail、Outlook、QQ邮箱、163邮箱等主流服务商
- 💌 **邮件收发**: 完整的邮件接收、发送、管理功能
- 🎨 **现代化界面**: 基于 Ant Design 的美观用户界面
- 🔄 **自动同步**: 可配置的自动邮件同步
- 🌙 **主题支持**: 支持浅色/深色/自动主题切换
- 🌍 **多语言**: 支持中文和英文界面

## 技术栈

- **前端**: React 18 + TypeScript + Ant Design
- **后端**: Rust + Tauri 2.0
- **数据库**: SQLite
- **邮件协议**: IMAP/SMTP
- **加密**: Ring (AES-256-GCM)

## 开发环境要求

- Node.js 18+
- Rust 1.70+
- pnpm/npm/yarn

## 快速开始

1. 克隆项目
```bash
git clone <repository-url>
cd 邮箱工具
```

2. 安装依赖
```bash
npm install
```

3. 启动开发服务器
```bash
npm run tauri dev
```

4. 构建应用
```bash
npm run tauri build
```

## 项目结构

```
邮箱工具/
├── src/                    # React 前端源码
│   ├── components/         # 可复用组件
│   ├── pages/             # 页面组件
│   ├── hooks/             # 自定义 hooks
│   ├── stores/            # 状态管理
│   ├── types/             # TypeScript 类型定义
│   └── styles/            # 样式文件
├── src-tauri/             # Rust 后端源码
│   ├── src/
│   │   ├── commands.rs    # Tauri 命令
│   │   ├── database.rs    # 数据库操作
│   │   ├── email/         # 邮件相关模块
│   │   ├── crypto.rs      # 加密解密
│   │   └── config.rs      # 配置管理
│   └── Cargo.toml
├── public/                # 静态资源
└── package.json
```

## 安全考虑

- 应用专用密码使用 AES-256-GCM 加密存储
- 支持主密钥保护，确保数据安全
- 所有网络通信使用 TLS/SSL 加密
- 本地数据库加密存储敏感信息

## 支持的邮箱服务商

- Gmail (需要开启两步验证并生成应用专用密码)
- Outlook/Hotmail
- QQ邮箱
- 163邮箱
- 126邮箱
- 其他支持 IMAP/SMTP 的邮箱服务

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
