use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailMessage {
    pub id: String,
    pub message_id: String,
    pub account_id: String,
    pub from: EmailAddress,
    pub to: Vec<EmailAddress>,
    pub cc: Option<Vec<EmailAddress>>,
    pub bcc: Option<Vec<EmailAddress>>,
    pub subject: String,
    pub body: String,
    pub html_body: Option<String>,
    pub date: DateTime<Utc>,
    pub read: bool,
    pub flagged: bool,
    pub has_attachment: bool,
    pub attachments: Option<Vec<Attachment>>,
    pub folder: String,
    pub size: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailAddress {
    pub name: Option<String>,
    pub email: String,
}

impl EmailAddress {
    pub fn new(email: String) -> Self {
        Self { name: None, email }
    }

    pub fn with_name(email: String, name: String) -> Self {
        Self {
            name: Some(name),
            email,
        }
    }

    pub fn display(&self) -> String {
        match &self.name {
            Some(name) => format!("{} <{}>", name, self.email),
            None => self.email.clone(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Attachment {
    pub id: String,
    pub filename: String,
    pub content_type: String,
    pub size: u64,
    pub cid: Option<String>, // Content-ID for inline attachments
    pub file_path: Option<String>, // Local storage path
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailFolder {
    pub name: String,
    pub display_name: String,
    pub message_count: u32,
    pub unread_count: u32,
    pub folder_type: FolderType,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FolderType {
    Inbox,
    Sent,
    Drafts,
    Trash,
    Spam,
    Custom,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SendEmailRequest {
    pub account_id: String,
    pub to: Vec<EmailAddress>,
    pub cc: Option<Vec<EmailAddress>>,
    pub bcc: Option<Vec<EmailAddress>>,
    pub subject: String,
    pub body: String,
    pub html_body: Option<String>,
    pub attachments: Option<Vec<String>>, // File paths
}

impl EmailMessage {
    pub fn new(
        message_id: String,
        account_id: String,
        from: EmailAddress,
        to: Vec<EmailAddress>,
        subject: String,
        body: String,
    ) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            message_id,
            account_id,
            from,
            to,
            cc: None,
            bcc: None,
            subject,
            body,
            html_body: None,
            date: Utc::now(),
            read: false,
            flagged: false,
            has_attachment: false,
            attachments: None,
            folder: "INBOX".to_string(),
            size: 0,
        }
    }

    pub fn mark_as_read(&mut self) {
        self.read = true;
    }

    pub fn toggle_flag(&mut self) {
        self.flagged = !self.flagged;
    }
}
