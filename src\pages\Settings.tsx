import { useState } from "react";
import { 
  Card, 
  Form, 
  Switch, 
  Select, 
  InputNumber, 
  Button, 
  Typography,
  Divider,
  Space,
  message 
} from "antd";
import { SettingOutlined, SaveOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;
const { Option } = Select;

interface AppSettings {
  autoRefresh: boolean;
  refreshInterval: number;
  notifications: boolean;
  theme: "light" | "dark" | "auto";
  language: "zh-CN" | "en-US";
  maxEmailsPerPage: number;
  enableSounds: boolean;
}

const Settings = () => {
  const [form] = Form.useForm();
  const [settings, setSettings] = useState<AppSettings>({
    autoRefresh: true,
    refreshInterval: 5,
    notifications: true,
    theme: "auto",
    language: "zh-CN",
    maxEmailsPerPage: 20,
    enableSounds: false,
  });

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setSettings(values);
      // TODO: 调用 Tauri 命令保存设置
      message.success("设置保存成功");
    } catch (error) {
      console.error("保存设置失败:", error);
      message.error("保存设置失败");
    }
  };

  const handleReset = () => {
    form.setFieldsValue(settings);
    message.info("设置已重置");
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 24 }}>
          <Title level={3} style={{ margin: 0 }}>
            <SettingOutlined style={{ marginRight: 8 }} />
            应用设置
          </Title>
        </div>

        <Form
          form={form}
          layout="vertical"
          initialValues={settings}
          onValuesChange={(changedValues, allValues) => {
            console.log("设置变更:", changedValues, allValues);
          }}
        >
          <Title level={4}>邮件设置</Title>
          
          <Form.Item
            name="autoRefresh"
            label="自动刷新邮件"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="refreshInterval"
            label="刷新间隔（分钟）"
            rules={[{ type: "number", min: 1, max: 60, message: "刷新间隔必须在1-60分钟之间" }]}
          >
            <InputNumber min={1} max={60} style={{ width: 120 }} />
          </Form.Item>

          <Form.Item
            name="maxEmailsPerPage"
            label="每页显示邮件数量"
            rules={[{ type: "number", min: 10, max: 100, message: "每页邮件数量必须在10-100之间" }]}
          >
            <InputNumber min={10} max={100} style={{ width: 120 }} />
          </Form.Item>

          <Divider />

          <Title level={4}>通知设置</Title>
          
          <Form.Item
            name="notifications"
            label="启用桌面通知"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="enableSounds"
            label="启用提示音"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Divider />

          <Title level={4}>界面设置</Title>
          
          <Form.Item
            name="theme"
            label="主题模式"
          >
            <Select style={{ width: 200 }}>
              <Option value="light">浅色模式</Option>
              <Option value="dark">深色模式</Option>
              <Option value="auto">跟随系统</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="language"
            label="语言"
          >
            <Select style={{ width: 200 }}>
              <Option value="zh-CN">简体中文</Option>
              <Option value="en-US">English</Option>
            </Select>
          </Form.Item>

          <Divider />

          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                icon={<SaveOutlined />}
                onClick={handleSave}
              >
                保存设置
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Settings;
