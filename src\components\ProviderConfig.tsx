import { Card, Descriptions, Tag, Typography } from 'antd';
import { CheckCircleOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface ProviderConfigProps {
  provider: string;
}

interface ProviderInfo {
  name: string;
  imapServer: string;
  imapPort: number;
  smtpServer: string;
  smtpPort: number;
  authMethod: string;
  setupGuide: string[];
}

const providerConfigs: Record<string, ProviderInfo> = {
  Gmail: {
    name: 'Gmail',
    imapServer: 'imap.gmail.com',
    imapPort: 993,
    smtpServer: 'smtp.gmail.com',
    smtpPort: 587,
    authMethod: '应用专用密码',
    setupGuide: [
      '1. 登录 Google 账户设置',
      '2. 启用两步验证',
      '3. 生成应用专用密码',
      '4. 使用应用专用密码登录'
    ]
  },
  Outlook: {
    name: 'Outlook',
    imapServer: 'outlook.office365.com',
    imapPort: 993,
    smtpServer: 'smtp-mail.outlook.com',
    smtpPort: 587,
    authMethod: '应用专用密码',
    setupGuide: [
      '1. 登录 Microsoft 账户',
      '2. 启用两步验证',
      '3. 生成应用密码',
      '4. 使用应用密码登录'
    ]
  },
  QQ: {
    name: 'QQ邮箱',
    imapServer: 'imap.qq.com',
    imapPort: 993,
    smtpServer: 'smtp.qq.com',
    smtpPort: 587,
    authMethod: '授权码',
    setupGuide: [
      '1. 登录 QQ 邮箱',
      '2. 进入设置 -> 账户',
      '3. 开启 IMAP/SMTP 服务',
      '4. 生成授权码'
    ]
  },
  '163': {
    name: '163邮箱',
    imapServer: 'imap.163.com',
    imapPort: 993,
    smtpServer: 'smtp.163.com',
    smtpPort: 587,
    authMethod: '授权码',
    setupGuide: [
      '1. 登录 163 邮箱',
      '2. 进入设置 -> POP3/SMTP/IMAP',
      '3. 开启 IMAP/SMTP 服务',
      '4. 设置授权码'
    ]
  },
  '126': {
    name: '126邮箱',
    imapServer: 'imap.126.com',
    imapPort: 993,
    smtpServer: 'smtp.126.com',
    smtpPort: 587,
    authMethod: '授权码',
    setupGuide: [
      '1. 登录 126 邮箱',
      '2. 进入设置 -> POP3/SMTP/IMAP',
      '3. 开启 IMAP/SMTP 服务',
      '4. 设置授权码'
    ]
  }
};

const ProviderConfig: React.FC<ProviderConfigProps> = ({ provider }) => {
  const config = providerConfigs[provider];

  if (!config) {
    return (
      <Card>
        <Text type="secondary">暂不支持此邮箱服务商的自动配置，请手动填写服务器信息。</Text>
      </Card>
    );
  }

  return (
    <div style={{ marginTop: 16 }}>
      <Card 
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <MailOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <span>{config.name} 配置信息</span>
          </div>
        }
        size="small"
      >
        <Descriptions column={2} size="small">
          <Descriptions.Item label="IMAP 服务器">
            {config.imapServer}
          </Descriptions.Item>
          <Descriptions.Item label="IMAP 端口">
            <Tag color="blue">{config.imapPort}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="SMTP 服务器">
            {config.smtpServer}
          </Descriptions.Item>
          <Descriptions.Item label="SMTP 端口">
            <Tag color="green">{config.smtpPort}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="加密方式">
            <Tag icon={<LockOutlined />} color="orange">SSL/TLS</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="认证方式">
            <Tag icon={<CheckCircleOutlined />} color="purple">{config.authMethod}</Tag>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card 
        title="设置指南" 
        size="small" 
        style={{ marginTop: 12 }}
      >
        <div>
          {config.setupGuide.map((step, index) => (
            <div key={index} style={{ marginBottom: 8 }}>
              <Text>{step}</Text>
            </div>
          ))}
        </div>
        <div style={{ marginTop: 12, padding: 12, backgroundColor: '#f6ffed', borderRadius: 6 }}>
          <Text type="success">
            <CheckCircleOutlined style={{ marginRight: 4 }} />
            完成设置后，请使用{config.authMethod}作为密码进行登录
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default ProviderConfig;
