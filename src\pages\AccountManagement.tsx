import { useState, useEffect } from "react";
import {
  Card,
  Button,
  Table,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Typography,
  Popconfirm,
  message,
  Alert,
  Spin
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined
} from "@ant-design/icons";
import { useAccountStore } from "../stores/useAccountStore";
import { useNotification } from "../hooks/useNotification";
import ProviderConfig from "../components/ProviderConfig";
import { EmailAccount } from "../types";

const { Title } = Typography;
const { Option } = Select;

const AccountManagement = () => {
  const {
    accounts,
    loading,
    error,
    fetchAccounts,
    addAccount,
    updateAccount,
    deleteAccount,
    testConnection,
    clearError
  } = useAccountStore();

  const { showSuccessNotification, showErrorNotification } = useNotification();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState<EmailAccount | null>(null);
  const [testingConnection, setTestingConnection] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<string>("Gmail");
  const [form] = Form.useForm();

  useEffect(() => {
    fetchAccounts();
  }, [fetchAccounts]);

  useEffect(() => {
    if (error) {
      showErrorNotification(error);
      clearError();
    }
  }, [error, showErrorNotification, clearError]);

  const handleAddAccount = () => {
    setEditingAccount(null);
    setSelectedProvider("Gmail");
    form.resetFields();
    form.setFieldsValue({ provider: "Gmail" });
    setIsModalVisible(true);
  };

  const handleEditAccount = (account: EmailAccount) => {
    setEditingAccount(account);
    setSelectedProvider(account.provider);
    form.setFieldsValue(account);
    setIsModalVisible(true);
  };

  const handleDeleteAccount = async (id: string) => {
    try {
      await deleteAccount(id);
      showSuccessNotification("账户删除成功");
    } catch (error) {
      showErrorNotification("删除账户失败");
    }
  };

  const handleTestConnection = async () => {
    try {
      const values = await form.validateFields();
      setTestingConnection(true);

      const testAccount: EmailAccount = {
        id: editingAccount?.id || "",
        ...values,
        status: "testing",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const success = await testConnection(testAccount);
      if (success) {
        showSuccessNotification("连接测试成功");
      } else {
        showErrorNotification("连接测试失败，请检查配置");
      }
    } catch (error) {
      showErrorNotification("连接测试失败");
    } finally {
      setTestingConnection(false);
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      if (editingAccount) {
        // 编辑账户
        const updatedAccount: EmailAccount = {
          ...editingAccount,
          ...values,
          updatedAt: new Date().toISOString(),
        };
        await updateAccount(updatedAccount);
        showSuccessNotification("账户更新成功");
      } else {
        // 添加新账户
        await addAccount(values);
        showSuccessNotification("账户添加成功");
      }

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error("保存账户失败:", error);
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "connected": return "green";
      case "disconnected": return "orange";
      case "error": return "red";
      case "testing": return "blue";
      default: return "default";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "connected": return "已连接";
      case "disconnected": return "未连接";
      case "error": return "连接错误";
      case "testing": return "测试中";
      default: return "未知";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "connected": return <CheckCircleOutlined />;
      case "disconnected": return <ExclamationCircleOutlined />;
      case "error": return <ExclamationCircleOutlined />;
      case "testing": return <SyncOutlined spin />;
      default: return null;
    }
  };

  const columns = [
    {
      title: "账户名称",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "邮箱地址",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "服务商",
      dataIndex: "provider",
      key: "provider",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      render: (status: string) => (
        <Space>
          {getStatusIcon(status)}
          <span style={{ color: getStatusColor(status) }}>
            {getStatusText(status)}
          </span>
        </Space>
      ),
    },
    {
      title: "操作",
      key: "actions",
      render: (record: EmailAccount) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => handleEditAccount(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个账户吗？"
            onConfirm={() => handleDeleteAccount(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="link" 
              danger 
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Title level={3} style={{ margin: 0 }}>
            <UserOutlined style={{ marginRight: 8 }} />
            邮箱账户管理
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddAccount}
            loading={loading}
          >
            添加账户
          </Button>
        </div>

        {error && (
          <Alert
            message="错误"
            description={error}
            type="error"
            showIcon
            closable
            onClose={clearError}
            style={{ marginBottom: 16 }}
          />
        )}

        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={accounts}
            rowKey="id"
            pagination={false}
            locale={{ emptyText: "暂无邮箱账户" }}
          />
        </Spin>
      </Card>

      <Modal
        title={editingAccount ? "编辑邮箱账户" : "添加邮箱账户"}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="保存"
        cancelText="取消"
        confirmLoading={loading}
        width={600}
        footer={[
          <Button key="cancel" onClick={handleModalCancel}>
            取消
          </Button>,
          <Button
            key="test"
            onClick={handleTestConnection}
            loading={testingConnection}
            disabled={loading}
          >
            测试连接
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleModalOk}
            loading={loading}
          >
            保存
          </Button>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ provider: "Gmail" }}
        >
          <Form.Item
            name="name"
            label="账户名称"
            rules={[{ required: true, message: "请输入账户名称" }]}
          >
            <Input placeholder="例如：个人邮箱" />
          </Form.Item>
          
          <Form.Item
            name="email"
            label="邮箱地址"
            rules={[
              { required: true, message: "请输入邮箱地址" },
              { type: "email", message: "请输入有效的邮箱地址" }
            ]}
          >
            <Input placeholder="<EMAIL>" />
          </Form.Item>
          
          <Form.Item
            name="provider"
            label="邮箱服务商"
            rules={[{ required: true, message: "请选择邮箱服务商" }]}
          >
            <Select
              placeholder="选择邮箱服务商"
              onChange={(value) => setSelectedProvider(value)}
            >
              <Option value="Gmail">Gmail</Option>
              <Option value="Outlook">Outlook</Option>
              <Option value="QQ">QQ邮箱</Option>
              <Option value="163">163邮箱</Option>
              <Option value="126">126邮箱</Option>
              <Option value="Other">其他</Option>
            </Select>
          </Form.Item>

          <ProviderConfig provider={selectedProvider} />
          
          <Form.Item
            name="appPassword"
            label="应用专用密码"
            rules={[{ required: true, message: "请输入应用专用密码" }]}
          >
            <Input.Password 
              placeholder="输入应用专用密码"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AccountManagement;
