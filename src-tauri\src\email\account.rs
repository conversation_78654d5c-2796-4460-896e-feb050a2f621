use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EmailAccount {
    pub id: String,
    pub name: String,
    pub email: String,
    pub provider: EmailProvider,
    pub imap_server: String,
    pub imap_port: u16,
    pub smtp_server: String,
    pub smtp_port: u16,
    pub use_ssl: bool,
    pub app_password: String, // 这将被加密存储
    pub status: AccountStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EmailProvider {
    Gmail,
    Outlook,
    QQ,
    #[serde(rename = "163")]
    NetEase163,
    #[serde(rename = "126")]
    NetEase126,
    Other,
}

impl EmailProvider {
    pub fn get_default_config(&self) -> (String, u16, String, u16) {
        match self {
            EmailProvider::Gmail => (
                "imap.gmail.com".to_string(),
                993,
                "smtp.gmail.com".to_string(),
                587,
            ),
            EmailProvider::Outlook => (
                "outlook.office365.com".to_string(),
                993,
                "smtp-mail.outlook.com".to_string(),
                587,
            ),
            EmailProvider::QQ => (
                "imap.qq.com".to_string(),
                993,
                "smtp.qq.com".to_string(),
                587,
            ),
            EmailProvider::NetEase163 => (
                "imap.163.com".to_string(),
                993,
                "smtp.163.com".to_string(),
                587,
            ),
            EmailProvider::NetEase126 => (
                "imap.126.com".to_string(),
                993,
                "smtp.126.com".to_string(),
                587,
            ),
            EmailProvider::Other => (
                "".to_string(),
                993,
                "".to_string(),
                587,
            ),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AccountStatus {
    Connected,
    Disconnected,
    Error,
    Testing,
}

impl EmailAccount {
    pub fn new(
        name: String,
        email: String,
        provider: EmailProvider,
        app_password: String,
    ) -> Self {
        let (imap_server, imap_port, smtp_server, smtp_port) = provider.get_default_config();
        let now = Utc::now();

        Self {
            id: uuid::Uuid::new_v4().to_string(),
            name,
            email,
            provider,
            imap_server,
            imap_port,
            smtp_server,
            smtp_port,
            use_ssl: true,
            app_password,
            status: AccountStatus::Disconnected,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn update_status(&mut self, status: AccountStatus) {
        self.status = status;
        self.updated_at = Utc::now();
    }
}
