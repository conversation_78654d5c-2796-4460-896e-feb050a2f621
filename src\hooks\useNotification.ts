import { useCallback } from 'react';
import { useSettingsStore } from '../stores/useSettingsStore';

interface NotificationOptions {
  title: string;
  body?: string;
  icon?: string;
  tag?: string;
  requireInteraction?: boolean;
}

export function useNotification() {
  const { settings } = useSettingsStore();

  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!('Notification' in window)) {
      console.warn('此浏览器不支持桌面通知');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }, []);

  const showNotification = useCallback(async (options: NotificationOptions): Promise<boolean> => {
    // 检查设置是否启用通知
    if (!settings.notifications) {
      return false;
    }

    // 检查浏览器支持
    if (!('Notification' in window)) {
      console.warn('此浏览器不支持桌面通知');
      return false;
    }

    // 请求权限
    const hasPermission = await requestPermission();
    if (!hasPermission) {
      console.warn('用户拒绝了通知权限');
      return false;
    }

    try {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || '/vite.svg',
        tag: options.tag,
        requireInteraction: options.requireInteraction,
      });

      // 可选：添加点击事件处理
      notification.onclick = () => {
        window.focus();
        notification.close();
      };

      // 自动关闭通知（5秒后）
      setTimeout(() => {
        notification.close();
      }, 5000);

      return true;
    } catch (error) {
      console.error('显示通知失败:', error);
      return false;
    }
  }, [settings.notifications, requestPermission]);

  const showEmailNotification = useCallback((from: string, subject: string) => {
    return showNotification({
      title: '新邮件',
      body: `来自: ${from}\n主题: ${subject}`,
      tag: 'new-email',
      requireInteraction: false,
    });
  }, [showNotification]);

  const showErrorNotification = useCallback((message: string) => {
    return showNotification({
      title: '错误',
      body: message,
      tag: 'error',
      requireInteraction: true,
    });
  }, [showNotification]);

  const showSuccessNotification = useCallback((message: string) => {
    return showNotification({
      title: '成功',
      body: message,
      tag: 'success',
      requireInteraction: false,
    });
  }, [showNotification]);

  return {
    showNotification,
    showEmailNotification,
    showErrorNotification,
    showSuccessNotification,
    requestPermission,
    isSupported: 'Notification' in window,
    permission: 'Notification' in window ? Notification.permission : 'unsupported',
  };
}
